<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arcaai EHR Application - Complete Architecture Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        body { 
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
        }
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
        }
        .architecture-diagram {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1.5rem;
            font-family: monospace;
            font-size: 0.75rem;
            white-space: pre;
            overflow-x: auto;
        }
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            margin: 2rem 0 1rem 0;
        }
        .subsection-header {
            background: #f1f5f9;
            border-left: 4px solid #3b82f6;
            padding: 0.75rem 1rem;
            margin: 1.5rem 0 1rem 0;
        }
        .feature-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 0.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .tech-stack-item {
            background: #f8fafc;
            border: 1px solid #cbd5e1;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin: 0.25rem;
            display: inline-block;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        th, td {
            border: 1px solid #e2e8f0;
            padding: 0.75rem;
            text-align: left;
        }
        th {
            background: #f1f5f9;
            font-weight: 600;
        }
        .warning-box {
            background: #fef3cd;
            border: 1px solid #fbbf24;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .info-box {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .success-box {
            background: #d1fae5;
            border: 1px solid #10b981;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="max-w-6xl mx-auto p-8">
        <!-- Header -->
        <header class="text-center mb-12">
            <h1 class="text-5xl font-bold text-gray-800 mb-4">
                <i class="fas fa-hospital text-blue-600 mr-4"></i>
                Arcaai EHR Application
            </h1>
            <h2 class="text-2xl text-gray-600 mb-6">Complete Architecture Documentation</h2>
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-lg">
                <p class="text-lg">Comprehensive healthcare management system built as a serverless microservice architecture on Microsoft Azure</p>
            </div>
        </header>

        <!-- Table of Contents -->
        <div class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-list mr-3"></i>Table of Contents
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="grid grid-cols-2 gap-4">
                <ul class="space-y-2">
                    <li><a href="#system-overview" class="text-blue-600 hover:underline"><i class="fas fa-server mr-2"></i>System Overview</a></li>
                    <li><a href="#technology-stack" class="text-blue-600 hover:underline"><i class="fas fa-layer-group mr-2"></i>Technology Stack</a></li>
                    <li><a href="#architecture-patterns" class="text-blue-600 hover:underline"><i class="fas fa-sitemap mr-2"></i>Architecture Patterns</a></li>
                    <li><a href="#application-structure" class="text-blue-600 hover:underline"><i class="fas fa-folder-tree mr-2"></i>Application Structure</a></li>
                    <li><a href="#data-layer" class="text-blue-600 hover:underline"><i class="fas fa-database mr-2"></i>Data Layer</a></li>
                    <li><a href="#service-layer" class="text-blue-600 hover:underline"><i class="fas fa-cogs mr-2"></i>Service Layer</a></li>
                    <li><a href="#api-layer" class="text-blue-600 hover:underline"><i class="fas fa-plug mr-2"></i>API Layer</a></li>
                </ul>
                <ul class="space-y-2">
                    <li><a href="#security" class="text-blue-600 hover:underline"><i class="fas fa-shield-alt mr-2"></i>Security & Authentication</a></li>
                    <li><a href="#external-integrations" class="text-blue-600 hover:underline"><i class="fas fa-link mr-2"></i>External Integrations</a></li>
                    <li><a href="#development-environment" class="text-blue-600 hover:underline"><i class="fas fa-code mr-2"></i>Development Environment</a></li>
                    <li><a href="#cicd-pipeline" class="text-blue-600 hover:underline"><i class="fas fa-rocket mr-2"></i>CI/CD Pipeline</a></li>
                    <li><a href="#deployment-architecture" class="text-blue-600 hover:underline"><i class="fas fa-cloud mr-2"></i>Deployment Architecture</a></li>
                    <li><a href="#monitoring-logging" class="text-blue-600 hover:underline"><i class="fas fa-chart-line mr-2"></i>Monitoring & Logging</a></li>
                    <li><a href="#performance-optimization" class="text-blue-600 hover:underline"><i class="fas fa-tachometer-alt mr-2"></i>Performance Optimization</a></li>
                </ul>
            </div>
        </div>

        <!-- System Overview -->
        <div id="system-overview" class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-server mr-3"></i>System Overview
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <p class="text-lg mb-6">The Arcaai EHR (Electronic Health Records) application is a comprehensive healthcare management system built as a serverless microservice architecture on Microsoft Azure. The system manages patient records, doctor profiles, appointments, lab tests, prescriptions, and lifestyle data with integrated payment processing and ABDM (Ayushman Bharat Digital Mission) compliance.</p>
            
            <div class="subsection-header">
                <h3 class="text-xl font-semibold flex items-center">
                    <i class="fas fa-star mr-2"></i>Key Features
                </h3>
            </div>
            <div class="grid grid-cols-2 gap-4">
                <div class="feature-card">
                    <h4 class="font-semibold text-blue-600 mb-2"><i class="fas fa-user-injured mr-2"></i>Patient Management</h4>
                    <p class="text-sm">Complete patient lifecycle management with medical history</p>
                </div>
                <div class="feature-card">
                    <h4 class="font-semibold text-blue-600 mb-2"><i class="fas fa-user-md mr-2"></i>Doctor Portal</h4>
                    <p class="text-sm">Doctor profiles, EMR customization, and consultation management</p>
                </div>
                <div class="feature-card">
                    <h4 class="font-semibold text-blue-600 mb-2"><i class="fas fa-calendar-check mr-2"></i>Appointment System</h4>
                    <p class="text-sm">Scheduling and queue management</p>
                </div>
                <div class="feature-card">
                    <h4 class="font-semibold text-blue-600 mb-2"><i class="fas fa-flask mr-2"></i>Lab Management</h4>
                    <p class="text-sm">Test ordering, report processing with OCR capabilities</p>
                </div>
                <div class="feature-card">
                    <h4 class="font-semibold text-blue-600 mb-2"><i class="fas fa-pills mr-2"></i>Prescription System</h4>
                    <p class="text-sm">Medicine management and prescription packages</p>
                </div>
                <div class="feature-card">
                    <h4 class="font-semibold text-blue-600 mb-2"><i class="fas fa-credit-card mr-2"></i>Payment Integration</h4>
                    <p class="text-sm">Razorpay payment gateway integration</p>
                </div>
                <div class="feature-card">
                    <h4 class="font-semibold text-blue-600 mb-2"><i class="fas fa-id-card mr-2"></i>ABDM Integration</h4>
                    <p class="text-sm">ABHA number generation and verification</p>
                </div>
                <div class="feature-card">
                    <h4 class="font-semibold text-blue-600 mb-2"><i class="fas fa-robot mr-2"></i>AI-Powered</h4>
                    <p class="text-sm">OpenAI integration for medical summaries and ambient listening</p>
                </div>
            </div>
        </div>

        <!-- Technology Stack -->
        <div id="technology-stack" class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-layer-group mr-3"></i>Technology Stack
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Backend Framework</h3>
                    </div>
                    <div class="space-y-2">
                        <div class="tech-stack-item">
                            <i class="fab fa-node-js text-green-600 mr-2"></i>Node.js 20
                        </div>
                        <div class="tech-stack-item">
                            <i class="fas fa-bolt text-yellow-600 mr-2"></i>Azure Functions v4 (Serverless)
                        </div>
                        <div class="tech-stack-item">
                            <i class="fab fa-js-square text-yellow-500 mr-2"></i>JavaScript (ES6+)
                        </div>
                    </div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Database & Storage</h3>
                    </div>
                    <div class="space-y-2">
                        <div class="tech-stack-item">
                            <i class="fas fa-database text-blue-600 mr-2"></i>Azure Cosmos DB (NoSQL)
                        </div>
                        <div class="tech-stack-item">
                            <i class="fas fa-memory text-red-600 mr-2"></i>Azure Redis Cache
                        </div>
                        <div class="tech-stack-item">
                            <i class="fas fa-file-archive text-purple-600 mr-2"></i>Azure Blob Storage
                        </div>
                        <div class="tech-stack-item">
                            <i class="fas fa-search text-gray-600 mr-2"></i>Cosmos DB SQL API
                        </div>
                    </div>
                </div>
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Cloud Platform</h3>
                    </div>
                    <div class="space-y-2">
                        <div class="tech-stack-item">
                            <i class="fab fa-microsoft text-blue-600 mr-2"></i>Microsoft Azure
                        </div>
                        <div class="tech-stack-item">
                            <i class="fas fa-server text-gray-600 mr-2"></i>Azure Functions (Consumption Plan)
                        </div>
                        <div class="tech-stack-item">
                            <i class="fas fa-gateway text-green-600 mr-2"></i>Azure API Management (APIM)
                        </div>
                        <div class="tech-stack-item">
                            <i class="fas fa-id-badge text-orange-600 mr-2"></i>Azure Active Directory B2C
                        </div>
                    </div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">External Services</h3>
                    </div>
                    <div class="space-y-2">
                        <div class="tech-stack-item">
                            <i class="fas fa-brain text-purple-600 mr-2"></i>Azure OpenAI (GPT-4)
                        </div>
                        <div class="tech-stack-item">
                            <i class="fas fa-credit-card text-blue-600 mr-2"></i>Razorpay Payment Gateway
                        </div>
                        <div class="tech-stack-item">
                            <i class="fas fa-eye text-green-600 mr-2"></i>Custom OCR Service
                        </div>
                        <div class="tech-stack-item">
                            <i class="fas fa-heart text-red-600 mr-2"></i>ICD-11, SNOMED CT, LOINC
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Architecture Patterns -->
        <div id="architecture-patterns" class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-sitemap mr-3"></i>Architecture Patterns
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="subsection-header">
                <h3 class="text-xl font-semibold">1. Clean Architecture</h3>
            </div>
            <p class="mb-4">The application follows clean architecture principles with clear separation of concerns:</p>
            <div class="architecture-diagram">┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│ ┌─────────────────┐ ┌─────────────────┐ ┌──────────────┐ │
│ │ Azure Functions │ │   API Gateway   │ │  Middleware  │ │
│ │  (Controllers)  │ │     (APIM)      │ │ (Auth/CORS)  │ │
│ └─────────────────┘ └─────────────────┘ └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  Business Logic Layer                       │
│ ┌─────────────────┐ ┌─────────────────┐ ┌──────────────┐ │
│ │    Handlers     │ │    Services     │ │   Utilities  │ │
│ │  (Use Cases)    │ │ (Domain Logic)  │ │   (Helpers)  │ │
│ └─────────────────┘ └─────────────────┘ └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Data Access Layer                         │
│ ┌─────────────────┐ ┌─────────────────┐ ┌──────────────┐ │
│ │  Repositories   │ │     Queries     │ │    Models    │ │
│ │(CRUD Operations)│ │(Read Operations)│ │(Data Schema) │ │
│ └─────────────────┘ └─────────────────┘ └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  Infrastructure Layer                       │
│ ┌─────────────────┐ ┌─────────────────┐ ┌──────────────┐ │
│ │   Cosmos DB     │ │  Redis Cache    │ │ Blob Storage │ │
│ │  (Database)     │ │   (Caching)     │ │ (Files/Docs) │ │
│ └─────────────────┘ └─────────────────┘ └──────────────┘ │
└─────────────────────────────────────────────────────────────┘</div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">2. Repository Pattern</h3>
                    </div>
                    <ul class="list-disc list-inside space-y-2">
                        <li><strong>Repositories:</strong> Handle CRUD operations and data persistence</li>
                        <li><strong>Queries:</strong> Optimized read operations with pagination and filtering</li>
                        <li><strong>Models:</strong> Data schema definitions and validation</li>
                    </ul>
                </div>
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">3. Service Layer Pattern</h3>
                    </div>
                    <ul class="list-disc list-inside space-y-2">
                        <li><strong>Services:</strong> Business logic and domain operations</li>
                        <li><strong>Handlers:</strong> Request/response processing and validation</li>
                        <li><strong>Utilities:</strong> Shared helper functions and utilities</li>
                    </ul>
                </div>
            </div>

            <div class="subsection-header">
                <h3 class="text-xl font-semibold">4. Microservice Architecture</h3>
            </div>
            <p>Each domain is organized as a separate microservice:</p>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
                <div class="bg-blue-50 p-3 rounded-lg text-center">
                    <i class="fas fa-user-injured text-blue-600 text-2xl mb-2"></i>
                    <p class="font-semibold">Patient Management Service</p>
                </div>
                <div class="bg-green-50 p-3 rounded-lg text-center">
                    <i class="fas fa-user-md text-green-600 text-2xl mb-2"></i>
                    <p class="font-semibold">Doctor Management Service</p>
                </div>
                <div class="bg-purple-50 p-3 rounded-lg text-center">
                    <i class="fas fa-calendar-check text-purple-600 text-2xl mb-2"></i>
                    <p class="font-semibold">Appointment Service</p>
                </div>
                <div class="bg-yellow-50 p-3 rounded-lg text-center">
                    <i class="fas fa-flask text-yellow-600 text-2xl mb-2"></i>
                    <p class="font-semibold">Lab Test Service</p>
                </div>
                <div class="bg-red-50 p-3 rounded-lg text-center">
                    <i class="fas fa-credit-card text-red-600 text-2xl mb-2"></i>
                    <p class="font-semibold">Payment Service</p>
                </div>
                <div class="bg-indigo-50 p-3 rounded-lg text-center">
                    <i class="fas fa-id-card text-indigo-600 text-2xl mb-2"></i>
                    <p class="font-semibold">ABDM Integration Service</p>
                </div>
            </div>
        </div>

        <!-- Application Structure -->
        <div id="application-structure" class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-folder-tree mr-3"></i>Application Structure
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="code-block">src/
├── auth/                    # Authentication utilities
├── common/                  # Shared utilities and constants
│   ├── auth-message.js     # Authentication messages  
│   ├── constant.js         # Application constants
│   ├── helper.js           # Common helper functions
│   ├── permissions.js      # Permission definitions
│   ├── roles.js            # Role definitions
│   └── user-validation.js  # User validation logic
├── cosmosDbContext/        # Database context and configuration
│   └── comosdb-context.js  # Cosmos DB client and utilities
├── functions/              # Azure Functions (API endpoints)
│   ├── index.js            # Main function app configuration
│   ├── auth.js             # Authentication endpoints
│   ├── patient.js          # Patient management endpoints
│   ├── doctor.js           # Doctor management endpoints
│   ├── appointment.js      # Appointment endpoints
│   ├── payment.js          # Payment endpoints
│   ├── abdm.js             # ABDM integration endpoints
│   └── [other-functions]   # Additional domain-specific endpoints
├── handlers/               # Business logic handlers
│   ├── patient-handler.js  # Patient business logic
│   ├── doctor-handler.js   # Doctor business logic
│   ├── payment-handler.js  # Payment processing logic
│   └── [other-handlers]    # Additional handlers
├── services/               # Domain services
│   ├── patient-service.js  # Patient domain service
│   ├── doctor-service.js   # Doctor domain service
│   ├── payment-service.js  # Payment service
│   ├── openai-service.js   # AI integration service
│   ├── b2c-service.js      # Azure B2C integration
│   └── [other-services]    # Additional services
├── repositories/           # Data access layer
│   ├── patient-repository.js # Patient data operations
│   ├── doctor-repository.js  # Doctor data operations
│   ├── payment-repository.js # Payment data operations
│   └── [other-repositories] # Additional repositories
├── queries/                # Optimized read operations
│   ├── patient-query.js    # Patient query operations
│   ├── medicine-query.js   # Medicine query operations
│   └── [other-queries]     # Additional queries
├── models/                 # Data models and schemas
│   ├── patient-model.js    # Patient data model
│   ├── doctor-model.js     # Doctor data model
│   └── [other-models]      # Additional models
├── utils/                  # Utility functions
│   ├── pagination.js       # Pagination utilities
│   ├── sanitization.js     # Data sanitization
│   └── [other-utils]       # Additional utilities
└── tasks/                  # Background tasks and cron jobs
    ├── finalize-patient-history-cron.js
    └── finalize-records-cron.js</div>
        </div>

        <!-- Data Layer -->
        <div id="data-layer" class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-database mr-3"></i>Data Layer
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="subsection-header">
                <h3 class="text-xl font-semibold">Cosmos DB Architecture</h3>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold text-lg mb-3">Database Configuration</h4>
                    <ul class="list-disc list-inside space-y-2">
                        <li><strong>Database Name:</strong> ArcaAudioLayer (configurable)</li>
                        <li><strong>API:</strong> SQL API (Core SQL)</li>
                        <li><strong>Consistency Level:</strong> Session (default)</li>
                        <li><strong>Development Throughput:</strong> 100-1000 RU/s</li>
                        <li><strong>Production Throughput:</strong> 400-4000 RU/s</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-lg mb-3">Performance Optimization</h4>
                    <ul class="list-disc list-inside space-y-2">
                        <li><strong>Batch Operations:</strong> Partition-aware batching (max 100 operations)</li>
                        <li><strong>Connection Pooling:</strong> Shared Cosmos DB client with connection reuse</li>
                        <li><strong>Query Optimization:</strong> Indexed queries with continuation tokens</li>
                        <li><strong>Caching:</strong> Redis cache for frequently accessed data</li>
                    </ul>
                </div>
            </div>

            <div class="subsection-header">
                <h3 class="text-xl font-semibold">Container Strategy</h3>
            </div>
            <p class="mb-4">Each domain has dedicated containers with optimized partition keys:</p>
            <div class="code-block">{
  "patients": { 
    partitionKey: "/id", 
    throughput: 400 
  },
  "doctors": { 
    partitionKey: "/id", 
    throughput: 400 
  },
  "appointments": { 
    partitionKey: "/doctorId", 
    throughput: 400 
  },
  "medicines": { 
    partitionKey: "/id", 
    throughput: 400 
  },
  "lab_tests": { 
    partitionKey: "/patientId", 
    throughput: 400 
  },
  "payments": { 
    partitionKey: "/organizationId", 
    throughput: 400 
  },
  "organizations": { 
    partitionKey: "/id", 
    throughput: 400 
  },
  "users": { 
    partitionKey: "/organizationId", 
    throughput: 400 
  }
}</div>

            <div class="subsection-header">
                <h3 class="text-xl font-semibold">Redis Cache Strategy</h3>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Cache Type</th>
                        <th>Data</th>
                        <th>TTL</th>
                        <th>Purpose</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Session Storage</td>
                        <td>User sessions and JWT tokens</td>
                        <td>1 hour</td>
                        <td>Authentication state</td>
                    </tr>
                    <tr>
                        <td>Reference Data</td>
                        <td>Medicine lists, lab test catalogs</td>
                        <td>24 hours</td>
                        <td>Frequently accessed static data</td>
                    </tr>
                    <tr>
                        <td>Temporary Data</td>
                        <td>OTP codes, password reset tokens</td>
                        <td>5-30 minutes</td>
                        <td>Short-lived verification data</td>
                    </tr>
                    <tr>
                        <td>Profile Cache</td>
                        <td>Doctor profiles, organization settings</td>
                        <td>30-60 minutes</td>
                        <td>User profile optimization</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Service Layer -->
        <div id="service-layer" class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-cogs mr-3"></i>Service Layer
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">1. Patient Service</h3>
                    </div>
                    <div class="code-block">// Patient lifecycle management
- createPatient()
- updatePatient()  
- getPatientProfile()
- searchPatients()
- managePatientHistory()
- managePatientVitals()</div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">2. Doctor Service</h3>
                    </div>
                    <div class="code-block">// Doctor management and EMR customization
- createDoctorProfile()
- updateDoctorProfile()
- customizeEMR()
- manageDoctorSchedule()</div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">3. Appointment Service</h3>
                    </div>
                    <div class="code-block">// Appointment scheduling and management
- scheduleAppointment()
- updateAppointment()
- cancelAppointment()
- getAppointmentQueue()</div>
                </div>
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">4. Payment Service</h3>
                    </div>
                    <div class="code-block">// Razorpay integration
- createPaymentOrder()
- verifyPayment()
- handleWebhook()
- getPaymentHistory()</div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">5. ABDM Service</h3>
                    </div>
                    <div class="code-block">// ABHA number management
- initiateAbhaCreation()
- verifyOTP()
- completeAbhaCreation()
- getAbhaDetails()</div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">External Service Integrations</h3>
                    </div>
                    <ul class="list-disc list-inside space-y-2">
                        <li><strong>OpenAI Service:</strong> Medical summaries, ambient listening, lifestyle analysis</li>
                        <li><strong>B2C Service:</strong> User management, authentication, JWT validation</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- API Layer -->
        <div id="api-layer" class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-plug mr-3"></i>API Layer
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="subsection-header">
                <h3 class="text-xl font-semibold">Azure Functions Configuration</h3>
            </div>
            <div class="code-block">{
  "version": "2.0",
  "extensionBundle": {
    "id": "Microsoft.Azure.Functions.ExtensionBundle",
    "version": "[4.*, 5.0.0)"
  },
  "logging": {
    "applicationInsights": {
      "samplingSettings": {
        "isEnabled": true,
        "excludedTypes": "Request"
      }
    }
  }
}</div>

            <div class="subsection-header">
                <h3 class="text-xl font-semibold">API Gateway (APIM) Configuration</h3>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Service</th>
                        <th>Base URL</th>
                        <th>Rate Limit</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Main API</td>
                        <td>https://emr-ms-dev-apim.azure-api.net/EMR-MS/api/v0.1</td>
                        <td>1000/hour</td>
                    </tr>
                    <tr>
                        <td>ABDM</td>
                        <td>https://emr-ms-dev-apim.azure-api.net/abdm/v0.1</td>
                        <td>1000/hour</td>
                    </tr>
                    <tr>
                        <td>Appointments</td>
                        <td>https://emr-ms-dev-apim.azure-api.net/appointment/v0.1</td>
                        <td>1000/hour</td>
                    </tr>
                </tbody>
            </table>

            <div class="info-box">
                <p><strong><i class="fas fa-info-circle mr-2"></i>API Features:</strong></p>
                <ul class="list-disc list-inside mt-2">
                    <li>CORS configured for cross-origin requests</li>
                    <li>JWT Bearer token validation</li>
                    <li>Rate limiting per API key</li>
                    <li>Comprehensive error handling</li>
                </ul>
            </div>

            <div class="subsection-header">
                <h3 class="text-xl font-semibold">Function Routing Strategy</h3>
            </div>
            <div class="code-block">// Example function definition
app.http('patient-profile', {
  methods: ['GET', 'POST', 'PATCH'],
  route: 'patient',
  authLevel: 'function',
  handler: async (req, context) => {
    return await patientHandler.handleRequest(req)
  }
})</div>
        </div>

        <!-- Security & Authentication -->
        <div id="security" class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-shield-alt mr-3"></i>Security & Authentication
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="subsection-header">
                <h3 class="text-xl font-semibold">Authentication Architecture</h3>
            </div>
            <div class="architecture-diagram">┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client App    │───▶│   Azure APIM    │───▶│ Azure Functions │
│                 │    │  (API Gateway)  │    │   (Backend)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Azure AD B2C   │    │ JWT Validation  │    │ Role-Based Auth │
│(Identity Mgmt)  │    │ & Rate Limit    │    │ & Permissions   │
└─────────────────┘    └─────────────────┘    └─────────────────┘</div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">JWT Token Structure</h3>
                    </div>
                    <div class="code-block">{
  "oid": "organization-id",
  "uid": "user-id", 
  "role": "doctor",
  "permissions": ["emr.access", "patient.view"],
  "exp": **********,
  "iat": **********
}</div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Data Security</h3>
                    </div>
                    <ul class="list-disc list-inside space-y-2">
                        <li><strong>Encryption at Rest:</strong> Cosmos DB automatic encryption</li>
                        <li><strong>Encryption in Transit:</strong> HTTPS/TLS 1.2+ for all communications</li>
                        <li><strong>Data Sanitization:</strong> Input validation and sanitization</li>
                        <li><strong>PII Protection:</strong> Patient data encryption and access logging</li>
                        <li><strong>Audit Logging:</strong> Comprehensive audit trail for all operations</li>
                    </ul>
                </div>
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Role-Based Access Control (RBAC)</h3>
                    </div>
                    <table class="text-sm">
                        <thead>
                            <tr>
                                <th>Role</th>
                                <th>Key Permissions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Super Admin</td>
                                <td>Full system access</td>
                            </tr>
                            <tr>
                                <td>Organization Admin</td>
                                <td>Organization management</td>
                            </tr>
                            <tr>
                                <td>Doctor</td>
                                <td>EMR access, patient management</td>
                            </tr>
                            <tr>
                                <td>Nurse</td>
                                <td>Patient care, basic EMR</td>
                            </tr>
                            <tr>
                                <td>Lab Technician</td>
                                <td>Lab test management</td>
                            </tr>
                            <tr>
                                <td>Receptionist</td>
                                <td>Appointment scheduling</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- External Integrations -->
        <div id="external-integrations" class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-link mr-3"></i>External Integrations
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">1. ABDM (Ayushman Bharat Digital Mission)</h3>
                    </div>
                    <div class="code-block">{
  baseUrl: 'https://abhasbx.abdm.gov.in/abha/api/v3',
  clientId: 'SBXID_009193',
  operations: [
    'initiate-aadhaar',
    'initiate-mobile', 
    'verify-otp',
    'complete-creation',
    'details-by-number'
  ]
}</div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">2. Razorpay Payment Gateway</h3>
                    </div>
                    <div class="code-block">{
  keyId: 'rzp_test_CUHJLaG7X7H8uG',
  paymentTypes: [
    'patient_registration',
    'consultation',
    'prescription', 
    'lab_tests'
  ]
}</div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">3. Azure OpenAI Service</h3>
                    </div>
                    <div class="code-block">{
  endpoint: 'https://erm-dev-openai.openai.azure.com',
  model: 'emrsummary4o',
  features: [
    'medical_summaries',
    'ambient_listening',
    'lifestyle_analysis'
  ]
}</div>
                </div>
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">4. Healthcare Standards Integration</h3>
                    </div>
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">
                                <i class="fas fa-book-medical mr-2"></i>ICD-11
                            </h4>
                            <p class="text-sm">International Classification of Diseases</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">
                                <i class="fas fa-stethoscope mr-2"></i>SNOMED CT
                            </h4>
                            <p class="text-sm">Systematized Nomenclature of Medicine Clinical Terms</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-2">
                                <i class="fas fa-microscope mr-2"></i>LOINC
                            </h4>
                            <p class="text-sm">Logical Observation Identifiers Names and Codes</p>
                        </div>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-orange-800 mb-2">
                                <i class="fas fa-exchange-alt mr-2"></i>HL7 FHIR
                            </h4>
                            <p class="text-sm">Healthcare data exchange standards</p>
                        </div>
                    </div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">5. OCR Service Integration</h3>
                    </div>
                    <div class="code-block">{
  endpoint: 'http://ocrcontainergroup-v1.eastus.azurecontainer.io:8000/ocr/',
  supportedFormats: ['PDF', 'JPEG', 'PNG'],
  features: [
    'text_extraction',
    'structured_data_parsing', 
    'medical_terminology_recognition'
  ]
}</div>
                </div>
            </div>
        </div>

        <!-- Development Environment -->
        <div id="development-environment" class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-code mr-3"></i>Development Environment
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Local Development Setup</h3>
                    </div>
                    <div class="success-box">
                        <h4 class="font-semibold mb-2"><i class="fas fa-check-circle mr-2"></i>Prerequisites</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li>Node.js 20+</li>
                            <li>Azure Functions Core Tools v4</li>
                            <li>Azure CLI</li>
                            <li>Docker Desktop</li>
                            <li>Visual Studio Code</li>
                        </ul>
                    </div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Environment Configuration</h3>
                    </div>
                    <div class="code-block">{
  "FUNCTIONS_WORKER_RUNTIME": "node",
  "COSMOS_DB_CONNECTIONSTRING": "local_cosmos_connection",
  "COSMOS_DB_DATABASE": "ArcaAudioLayer", 
  "OPENAI_ENDPOINT": "azure_openai_endpoint",
  "CLIENT_ID": "azure_b2c_client_id",
  "TENANT_ID": "azure_tenant_id",
  "cosmos_running_mode": "emulator"
}</div>
                </div>
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">VS Code Configuration</h3>
                    </div>
                    <div class="code-block">// .vscode/settings.json
{
  "azureFunctions.deploySubpath": ".",
  "azureFunctions.projectLanguage": "JavaScript", 
  "azureFunctions.projectRuntime": "~4",
  "azureFunctions.projectLanguageModel": 4
}</div>

                    <div class="code-block">// .vscode/launch.json
{
  "configurations": [{
    "name": "Attach to Node Functions",
    "type": "node",
    "request": "attach", 
    "port": 9229,
    "preLaunchTask": "func: host start"
  }]
}</div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Package Dependencies</h3>
                    </div>
                    <div class="space-y-2">
                        <div class="tech-stack-item">@azure/cosmos ^4.0.0</div>
                        <div class="tech-stack-item">@azure/functions ^4.5.0</div>
                        <div class="tech-stack-item">@azure/identity ^4.3.0</div>
                        <div class="tech-stack-item">axios ^1.7.2</div>
                        <div class="tech-stack-item">jsonwebtoken ^9.0.2</div>
                        <div class="tech-stack-item">razorpay ^2.9.6</div>
                        <div class="tech-stack-item">redis ^4.7.0</div>
                        <div class="tech-stack-item">openai ^4.38.5</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CI/CD Pipeline -->
        <div id="cicd-pipeline" class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-rocket mr-3"></i>CI/CD Pipeline
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="subsection-header">
                <h3 class="text-xl font-semibold">Azure DevOps Pipeline Configuration</h3>
            </div>
            <div class="code-block">trigger:
  - main
  - develop  
  - test

pool:
  vmImage: 'ubuntu-latest'

variables:
  - group: emr-dev-variables
  - name: containerRegistry
    value: 'ermdevcontainer'
  - name: imageRepository
    value: 'emr-v01/emr-ms'

stages:
  - stage: Build
    displayName: 'Build and Push Docker Image'
    jobs:
      - job: Build
        steps:
          - task: Docker@2
            displayName: 'Build and Push Docker Image'
            inputs:
              containerRegistry: '$(containerRegistry)'
              repository: '$(imageRepository)'
              command: 'buildAndPush'
              Dockerfile: 'Dockerfile'
              tags: |
                $(Build.SourceBranchName).$(Build.BuildId)
                latest</div>

            <div class="subsection-header">
                <h3 class="text-xl font-semibold">Dockerfile Configuration</h3>
            </div>
            <div class="code-block">FROM mcr.microsoft.com/azure-functions/node:4-nightly-node20

ENV AzureWebJobsScriptRoot=/home/<USER>/wwwroot \
    AzureFunctionsJobHost__Logging__Console__IsEnabled=true

COPY . /home/<USER>/wwwroot

RUN cd /home/<USER>/wwwroot && \
    npm install --production

EXPOSE 80</div>

            <div class="subsection-header">
                <h3 class="text-xl font-semibold">Environment Configuration</h3>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Environment</th>
                        <th>Resource Group</th>
                        <th>Function App</th>
                        <th>Cosmos DB</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Development</td>
                        <td>emr-dev-rg</td>
                        <td>emr-dev-functions</td>
                        <td>emr-dev-cosmosdb</td>
                    </tr>
                    <tr>
                        <td>Test</td>
                        <td>emr-test-rg</td>
                        <td>emr-test-functions</td>
                        <td>emr-test-cosmosdb</td>
                    </tr>
                    <tr>
                        <td>Production</td>
                        <td>emr-prod-rg</td>
                        <td>emr-prod-functions</td>
                        <td>emr-prod-cosmosdb</td>
                    </tr>
                </tbody>
            </table>

            <div class="info-box">
                <h4 class="font-semibold mb-2"><i class="fas fa-info-circle mr-2"></i>Deployment Process</h4>
                <ol class="list-decimal list-inside space-y-1">
                    <li>Build Trigger: Code commit to main/develop/test branches</li>
                    <li>Docker Build: Create containerized application image</li>
                    <li>Image Push: Push to Azure Container Registry</li>
                    <li>Release Trigger: Manual deployment trigger</li>
                    <li>Environment Selection: Choose target environment</li>
                    <li>Configuration Update: Environment-specific settings</li>
                    <li>Function App Deployment: Deploy container to Azure Functions</li>
                    <li>Health Check: Verify deployment success</li>
                    <li>Rollback: Automatic rollback on failure</li>
                </ol>
            </div>
        </div>

        <!-- Deployment Architecture -->
        <div id="deployment-architecture" class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-cloud mr-3"></i>Deployment Architecture
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="subsection-header">
                <h3 class="text-xl font-semibold">Azure Infrastructure</h3>
            </div>
            <div class="code-block">emr-dev-rg/
├── emr-dev-functions (Function App)
├── emr-dev-cosmosdb (Cosmos DB Account) 
├── emr-dev-apim (API Management)
├── ermdevstoragedata (Storage Account)
├── emrdevcache (Redis Cache)
├── ermdevcontainer (Container Registry)
└── emr-dev-openai (OpenAI Service)</div>

            <div class="subsection-header">
                <h3 class="text-xl font-semibold">Network Architecture</h3>
            </div>
            <div class="architecture-diagram">┌─────────────────────────────────────────────────────────────┐
│                     Internet Gateway                        │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Azure API Management                        │
│ ┌─────────────────┐ ┌─────────────────┐ ┌──────────────┐ │
│ │  Rate Limiting  │ │ Authentication  │ │     CORS     │ │
│ └─────────────────┘ └─────────────────┘ └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  Azure Functions                            │
│ ┌─────────────────┐ ┌─────────────────┐ ┌──────────────┐ │
│ │  Patient APIs   │ │  Doctor APIs    │ │ Payment APIs │ │
│ └─────────────────┘ └─────────────────┘ └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                    Data Layer                               │
│ ┌─────────────────┐ ┌─────────────────┐ ┌──────────────┐ │
│ │   Cosmos DB     │ │  Redis Cache    │ │ Blob Storage │ │
│ └─────────────────┘ └─────────────────┘ └──────────────┘ │
└─────────────────────────────────────────────────────────────┘</div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Scaling Configuration</h3>
                    </div>
                    <div class="code-block">// Function App Scaling
{
  "functionAppScaleLimit": 200,
  "routingRules": {
    "maxOutstandingRequests": 100,
    "maxConcurrentRequests": 50
  },
  "healthCheck": {
    "path": "/api/health",
    "interval": "00:00:30"
  }
}</div>
                </div>
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Cosmos DB Scaling</h3>
                    </div>
                    <div class="code-block">// Cosmos DB Scaling
{
  "throughput": {
    "development": "100-1000 RU/s",
    "production": "400-4000 RU/s"
  },
  "autoscale": true,
  "maxThroughput": 4000
}</div>
                </div>
            </div>
        </div>

        <!-- Monitoring & Logging -->
        <div id="monitoring-logging" class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-chart-line mr-3"></i>Monitoring & Logging
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Application Insights Configuration</h3>
                    </div>
                    <div class="code-block">{
  "applicationInsights": {
    "samplingSettings": {
      "isEnabled": true,
      "excludedTypes": "Request"
    },
    "enableLiveMetrics": true,
    "enableDependencyTracking": true,
    "enablePerformanceCounters": true
  }
}</div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Logging Strategy</h3>
                    </div>
                    <div class="code-block">// Structured Logging
const logger = {
  info: (message, data) => console.log(JSON.stringify({
    level: 'INFO',
    timestamp: new Date().toISOString(),
    message,
    data,
    correlationId: context.correlationId
  })),
  error: (message, error, data) => console.error(JSON.stringify({
    level: 'ERROR',
    timestamp: new Date().toISOString(),
    message,
    error: error.message,
    stack: error.stack,
    data,
    correlationId: context.correlationId
  }))
}</div>
                </div>
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Key Metrics & Alerts</h3>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold text-blue-600 mb-2">Metrics</h4>
                            <ul class="list-disc list-inside text-sm space-y-1">
                                <li>Function Execution Count</li>
                                <li>Function Execution Duration</li>
                                <li>Function Failures</li>
                                <li>Cosmos DB Request Units</li>
                                <li>Redis Cache Hit Rate</li>
                                <li>API Response Times</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-semibold text-red-600 mb-2">Alerts</h4>
                            <ul class="list-disc list-inside text-sm space-y-1">
                                <li>Function failure rate > 5%</li>
                                <li>Average response time > 5 seconds</li>
                                <li>Cosmos DB throttling detected</li>
                                <li>Redis cache unavailable</li>
                                <li>High memory usage (>80%)</li>
                                <li>Authentication failure spike</li>
                            </ul>
                        </div>
                    </div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Health Check Implementation</h3>
                    </div>
                    <div class="code-block">// Health Check Endpoint
const health = {
  status: 'healthy',
  timestamp: new Date().toISOString(),
  services: {
    cosmosDb: await checkCosmosDbHealth(),
    redis: await checkRedisHealth(),
    openai: await checkOpenAIHealth(),
    storage: await checkStorageHealth()
  }
}</div>
                </div>
            </div>
        </div>

        <!-- Performance Optimization -->
        <div id="performance-optimization" class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-tachometer-alt mr-3"></i>Performance Optimization
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Database Optimization</h3>
                    </div>
                    <div class="code-block">// Cosmos DB Performance Tuning
{
  // Connection Management
  connectionPolicy: {
    connectionMode: 'Gateway',
    maxConnectionPoolSize: 50,
    requestTimeout: 30000
  },
  // Query Optimization
  queryOptions: {
    enableCrossPartitionQuery: false,
    maxItemCount: 100,
    maxDegreeOfParallelism: 10
  },
  // Batch Operations
  batchConfig: {
    maxBatchSize: 100,
    maxConcurrency: 10,
    retryOptions: {
      maxRetryAttempts: 3,
      fixedRetryIntervalInMilliseconds: 1000
    }
  }
}</div>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">API Performance</h3>
                    </div>
                    <div class="code-block">// Response Optimization
{
  // Pagination
  defaultPageSize: 20,
  maxPageSize: 100,
  // Field Selection
  selectFields: ['id', 'name', 'email'], // Only required fields
  // Compression  
  enableGzipCompression: true,
  // Connection Pooling
  keepAlive: true,
  maxSockets: 50
}</div>
                </div>
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Caching Strategy</h3>
                    </div>
                    <table class="text-sm">
                        <thead>
                            <tr>
                                <th>Data Type</th>
                                <th>TTL</th>
                                <th>Purpose</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>User Sessions</td>
                                <td>3600s</td>
                                <td>Authentication</td>
                            </tr>
                            <tr>
                                <td>Medicines</td>
                                <td>86400s</td>
                                <td>Reference Data</td>
                            </tr>
                            <tr>
                                <td>Lab Tests</td>
                                <td>86400s</td>
                                <td>Reference Data</td>
                            </tr>
                            <tr>
                                <td>Doctor Profiles</td>
                                <td>1800s</td>
                                <td>Frequent Access</td>
                            </tr>
                            <tr>
                                <td>OTP Codes</td>
                                <td>300s</td>
                                <td>Temporary Data</td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Background Processing</h3>
                    </div>
                    <div class="code-block">// Cron Jobs Configuration
{
  "finalizePatientRecords": {
    "schedule": "0 0 2 * * *", // Daily at 2 AM  
    "function": "finalize-patient-history-cron"
  },
  "finalizeLifestyleRecords": {
    "schedule": "0 0 3 * * *", // Daily at 3 AM
    "function": "finalize-records-cron"
  },
  "cleanupTempFiles": {
    "schedule": "0 0 1 * * *", // Daily at 1 AM
    "function": "cleanup-temp-files"
  }
}</div>
                </div>
            </div>
        </div>

        <!-- Security Best Practices -->
        <div class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-lock mr-3"></i>Security Best Practices
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Data Protection</h3>
                    </div>
                    <ul class="list-disc list-inside space-y-2 text-sm">
                        <li><strong>Encryption:</strong> All data encrypted at rest and in transit</li>
                        <li><strong>Access Control:</strong> Role-based access with least privilege</li>
                        <li><strong>Audit Logging:</strong> Comprehensive audit trail</li>
                        <li><strong>Data Masking:</strong> PII data masked in logs</li>
                        <li><strong>Backup & Recovery:</strong> Automated backups with point-in-time recovery</li>
                    </ul>
                </div>
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">API Security</h3>
                    </div>
                    <ul class="list-disc list-inside space-y-2 text-sm">
                        <li><strong>Authentication:</strong> JWT-based with Azure AD B2C</li>
                        <li><strong>Authorization:</strong> Role-based permissions</li>
                        <li><strong>Rate Limiting:</strong> API throttling to prevent abuse</li>
                        <li><strong>Input Validation:</strong> Comprehensive sanitization</li>
                        <li><strong>CORS:</strong> Properly configured cross-origin sharing</li>
                    </ul>
                </div>
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Infrastructure Security</h3>
                    </div>
                    <ul class="list-disc list-inside space-y-2 text-sm">
                        <li><strong>Network Security:</strong> Private endpoints and VNet integration</li>
                        <li><strong>Key Management:</strong> Azure Key Vault for secrets</li>
                        <li><strong>Monitoring:</strong> Real-time security monitoring</li>
                        <li><strong>Compliance:</strong> HIPAA and healthcare data protection</li>
                        <li><strong>Incident Response:</strong> Automated detection and response</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Disaster Recovery -->
        <div class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-shield-alt mr-3"></i>Disaster Recovery & Business Continuity
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Backup Strategy</h3>
                    </div>
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">Cosmos DB</h4>
                            <ul class="text-sm space-y-1">
                                <li>• Automatic backups every 4 hours</li>
                                <li>• Point-in-time restore capability</li>
                                <li>• Geo-redundant backup storage</li>
                                <li>• 30-day retention period</li>
                            </ul>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">Blob Storage</h4>
                            <ul class="text-sm space-y-1">
                                <li>• Geo-redundant storage (GRS)</li>
                                <li>• Cross-region replication</li>
                                <li>• Versioning enabled</li>
                                <li>• Soft delete protection</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Recovery Procedures</h3>
                    </div>
                    <div class="warning-box">
                        <h4 class="font-semibold mb-2"><i class="fas fa-clock mr-2"></i>Recovery Objectives</h4>
                        <ul class="space-y-1">
                            <li><strong>RTO (Recovery Time Objective):</strong> 4 hours</li>
                            <li><strong>RPO (Recovery Point Objective):</strong> 1 hour</li>
                        </ul>
                    </div>
                    <ul class="list-disc list-inside space-y-2 text-sm">
                        <li><strong>Failover Process:</strong> Automated failover to secondary region</li>
                        <li><strong>Data Recovery:</strong> Point-in-time restore from backups</li>
                        <li><strong>Testing:</strong> Monthly disaster recovery testing</li>
                        <li><strong>Documentation:</strong> Comprehensive recovery procedures</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Future Enhancements -->
        <div class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-rocket mr-3"></i>Future Enhancements
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Planned Features</h3>
                    </div>
                    <div class="space-y-3">
                        <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-3 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-1">
                                <i class="fas fa-mobile-alt mr-2"></i>Mobile Application
                            </h4>
                            <p class="text-sm">React Native mobile app for doctors and patients</p>
                        </div>
                        <div class="bg-gradient-to-r from-green-50 to-green-100 p-3 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-1">
                                <i class="fas fa-video mr-2"></i>Telemedicine
                            </h4>
                            <p class="text-sm">Video consultation integration</p>
                        </div>
                        <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-3 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-1">
                                <i class="fas fa-brain mr-2"></i>AI Diagnostics
                            </h4>
                            <p class="text-sm">Advanced AI-powered diagnostic assistance</p>
                        </div>
                        <div class="bg-gradient-to-r from-orange-50 to-orange-100 p-3 rounded-lg">
                            <h4 class="font-semibold text-orange-800 mb-1">
                                <i class="fas fa-wifi mr-2"></i>IoT Integration
                            </h4>
                            <p class="text-sm">Medical device data integration</p>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="subsection-header">
                        <h3 class="text-xl font-semibold">Scalability Roadmap</h3>
                    </div>
                    <div class="space-y-3">
                        <div class="bg-gradient-to-r from-indigo-50 to-indigo-100 p-3 rounded-lg">
                            <h4 class="font-semibold text-indigo-800 mb-1">
                                <i class="fas fa-cubes mr-2"></i>Microservices
                            </h4>
                            <p class="text-sm">Further decomposition into domain-specific microservices</p>
                        </div>
                        <div class="bg-gradient-to-r from-teal-50 to-teal-100 p-3 rounded-lg">
                            <h4 class="font-semibold text-teal-800 mb-1">
                                <i class="fas fa-bolt mr-2"></i>Event-Driven Architecture
                            </h4>
                            <p class="text-sm">Implementation of event sourcing and CQRS</p>
                        </div>
                        <div class="bg-gradient-to-r from-red-50 to-red-100 p-3 rounded-lg">
                            <h4 class="font-semibold text-red-800 mb-1">
                                <i class="fas fa-globe mr-2"></i>Multi-Region
                            </h4>
                            <p class="text-sm">Global deployment with regional data residency</p>
                        </div>
                        <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 p-3 rounded-lg">
                            <h4 class="font-semibold text-yellow-800 mb-1">
                                <i class="fas fa-dharmachakra mr-2"></i>Kubernetes
                            </h4>
                            <p class="text-sm">Migration to Azure Kubernetes Service for better orchestration</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Conclusion -->
        <div class="section-header">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-flag-checkered mr-3"></i>Conclusion
            </h2>
        </div>
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <p class="text-lg mb-6">The Arcaai EHR application represents a modern, scalable, and secure healthcare management system built on Microsoft Azure's serverless architecture. The system leverages clean architecture principles, comprehensive security measures, and industry-standard healthcare integrations to provide a robust platform for healthcare providers.</p>
            
            <div class="subsection-header">
                <h3 class="text-xl font-semibold">Key Architectural Strengths</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-3">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-800 mb-2">
                            <i class="fas fa-server mr-2"></i>Serverless Architecture
                        </h4>
                        <p class="text-sm">Cost-effective and automatically scalable infrastructure</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-green-800 mb-2">
                            <i class="fas fa-layer-group mr-2"></i>Clean Architecture
                        </h4>
                        <p class="text-sm">Maintainable and testable codebase with clear separation of concerns</p>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-purple-800 mb-2">
                            <i class="fas fa-shield-alt mr-2"></i>Security-First
                        </h4>
                        <p class="text-sm">Comprehensive security and compliance measures</p>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="bg-orange-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-orange-800 mb-2">
                            <i class="fas fa-heart mr-2"></i>Healthcare Standards
                        </h4>
                        <p class="text-sm">Integration with ICD-11, SNOMED CT, and ABDM</p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-red-800 mb-2">
                            <i class="fas fa-robot mr-2"></i>AI-Powered
                        </h4>
                        <p class="text-sm">Advanced AI capabilities for medical insights and automation</p>
                    </div>
                    <div class="bg-indigo-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-indigo-800 mb-2">
                            <i class="fas fa-tools mr-2"></i>DevOps Ready
                        </h4>
                        <p class="text-sm">Automated CI/CD pipeline with comprehensive monitoring</p>
                    </div>
                </div>
            </div>
            
            <div class="success-box mt-6">
                <p class="text-center text-lg font-semibold">
                    <i class="fas fa-check-circle mr-2"></i>
                    The architecture is designed to support the growing needs of healthcare organizations while maintaining high performance, security, and reliability standards.
                </p>
            </div>
        </div>

        <!-- Footer -->
        <footer class="text-center py-8 border-t border-gray-200">
            <p class="text-gray-600">
                <i class="fas fa-calendar mr-2"></i>
                Documentation Generated: <span class="font-semibold" id="current-date"></span>
            </p>
            <p class="text-gray-500 mt-2">Arcaai EHR Application - Complete Architecture Documentation</p>
        </footer>
    </div>

    <script>
        // Set current date
        document.getElementById('current-date').textContent = new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long', 
            day: 'numeric'
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>