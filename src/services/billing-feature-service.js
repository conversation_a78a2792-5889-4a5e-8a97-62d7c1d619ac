const { logError, logInfo } = require('../common/logging')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const rolePermissionService = require('./role-permission-service')
const { PaymentType } = require('../common/constant')

const billingFeatureContainerId = 'BillingFeatureAccess'

class BillingFeatureService {
  /**
   * Get billing feature access settings for a user
   * @param {string} userId - User ID
   * @param {string} organizationId - Organization ID
   * @returns {Object} Billing feature access settings
   */
  async getBillingFeatureAccess(userId, organizationId) {
    try {
      logInfo(`Getting billing feature access for user: ${userId}, organization: ${organizationId}`)
      
      const query = `SELECT * FROM c WHERE c.userId = '${userId}' AND c.organizationId = '${organizationId}'`
      const data = await cosmosDbContext.queryItems(query, billingFeatureContainerId)
      
      if (data && data.length > 0) {
        return data[0]
      }
      
      // If no custom settings found, return default based on role permissions
      return await this.getDefaultBillingFeatureAccess(userId, organizationId)
    } catch (error) {
      logError(`Unable to get billing feature access for user ${userId}`, error)
      return null
    }
  }

  /**
   * Create or update billing feature access settings for a user
   * @param {Object} billingFeatureData - Billing feature access data
   * @returns {Object} Created/updated billing feature access
   */
  async upsertBillingFeatureAccess(billingFeatureData) {
    try {
      logInfo(`Upserting billing feature access: ${JSON.stringify(billingFeatureData)}`)
      
      // Check if record already exists
      const existingQuery = `SELECT * FROM c WHERE c.userId = '${billingFeatureData.userId}' AND c.organizationId = '${billingFeatureData.organizationId}'`
      const existingData = await cosmosDbContext.queryItems(existingQuery, billingFeatureContainerId)
      
      if (existingData && existingData.length > 0) {
        // Update existing record
        const existingRecord = existingData[0]
        const updatedData = {
          ...existingRecord,
          ...billingFeatureData,
          updated_on: new Date().toISOString()
        }
        return await cosmosDbContext.upsertItem(existingRecord.id, updatedData, billingFeatureContainerId)
      } else {
        // Create new record
        const newRecord = {
          ...billingFeatureData,
          id: require('uuid').v4(),
          created_on: new Date().toISOString(),
          updated_on: new Date().toISOString()
        }
        return await cosmosDbContext.createItem(newRecord, billingFeatureContainerId)
      }
    } catch (error) {
      logError(`Unable to upsert billing feature access`, error)
      return null
    }
  }

  /**
   * Get default billing feature access based on user's role permissions
   * @param {string} userId - User ID
   * @param {string} organizationId - Organization ID
   * @returns {Object} Default billing feature access settings
   */
  async getDefaultBillingFeatureAccess(userId, organizationId) {
    try {
      logInfo(`Getting default billing feature access for user: ${userId}`)
      
      // Get user's role permissions
      const userQuery = `SELECT c.roleId FROM c WHERE c.id = '${userId}'`
      const userData = await cosmosDbContext.queryItems(userQuery, 'Users')
      
      if (!userData || userData.length === 0) {
        logError(`User not found: ${userId}`)
        return this.getEmptyBillingFeatureAccess(userId, organizationId)
      }
      
      const roleId = userData[0].roleId
      if (!roleId) {
        logError(`No role found for user: ${userId}`)
        return this.getEmptyBillingFeatureAccess(userId, organizationId)
      }
      
      // Get role permissions
      const rolePermissions = await rolePermissionService.getRoleByIdAndOrganization(roleId, organizationId)
      
      if (!rolePermissions || !rolePermissions.APIs) {
        logError(`No role permissions found for roleId: ${roleId}`)
        return this.getEmptyBillingFeatureAccess(userId, organizationId)
      }
      
      // Check which billing permissions the role has
      const billingPermissions = this.extractBillingPermissions(rolePermissions.APIs)
      
      return {
        userId,
        organizationId,
        billingFeatures: {
          patientRegistration: billingPermissions.includes('billing.patient-registration.access'),
          appointmentBooking: billingPermissions.includes('billing.appointment-booking.access'),
          labMaster: billingPermissions.includes('billing.lab-master.access'),
          prescription: billingPermissions.includes('billing.prescription.access')
        },
        isDefault: true,
        created_on: new Date().toISOString(),
        updated_on: new Date().toISOString()
      }
    } catch (error) {
      logError(`Unable to get default billing feature access for user ${userId}`, error)
      return this.getEmptyBillingFeatureAccess(userId, organizationId)
    }
  }

  /**
   * Extract billing permissions from role APIs
   * @param {Array} roleAPIs - Role API permissions
   * @returns {Array} Array of billing permission keys
   */
  extractBillingPermissions(roleAPIs) {
    const billingPermissionKeys = [
      'billing.patient-registration.access',
      'billing.appointment-booking.access',
      'billing.lab-master.access',
      'billing.prescription.access'
    ]
    
    return roleAPIs
      .filter(api => api.permissionKey && billingPermissionKeys.includes(api.permissionKey))
      .map(api => api.permissionKey)
  }

  /**
   * Get empty billing feature access object
   * @param {string} userId - User ID
   * @param {string} organizationId - Organization ID
   * @returns {Object} Empty billing feature access
   */
  getEmptyBillingFeatureAccess(userId, organizationId) {
    return {
      userId,
      organizationId,
      billingFeatures: {
        patientRegistration: false,
        appointmentBooking: false,
        labMaster: false,
        prescription: false
      },
      isDefault: true,
      created_on: new Date().toISOString(),
      updated_on: new Date().toISOString()
    }
  }

  /**
   * Check if user has access to a specific billing feature
   * @param {string} userId - User ID
   * @param {string} organizationId - Organization ID
   * @param {string} paymentType - Payment type from PaymentType enum
   * @returns {boolean} Whether user has access to the billing feature
   */
  async hasAccessToBillingFeature(userId, organizationId, paymentType) {
    try {
      const billingAccess = await this.getBillingFeatureAccess(userId, organizationId)
      
      if (!billingAccess || !billingAccess.billingFeatures) {
        return false
      }
      
      const featureMap = {
        [PaymentType.PATIENT_REGISTRATION]: 'patientRegistration',
        [PaymentType.CONSULTATION]: 'appointmentBooking',
        [PaymentType.LAB_TEST]: 'labMaster',
        [PaymentType.PRESCRIPTION]: 'prescription'
      }
      
      const featureKey = featureMap[paymentType]
      if (!featureKey) {
        logError(`Unknown payment type: ${paymentType}`)
        return false
      }
      
      return billingAccess.billingFeatures[featureKey] === true
    } catch (error) {
      logError(`Error checking billing feature access for user ${userId}, paymentType ${paymentType}`, error)
      return false
    }
  }
}

module.exports = new BillingFeatureService()
