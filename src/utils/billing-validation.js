const billingFeatureService = require('../services/billing-feature-service')
const { logError, logInfo } = require('../common/logging')
const { PaymentType } = require('../common/constant')

/**
 * Validate if a user has access to create a payment order for a specific payment type
 * @param {string} userId - User ID
 * @param {string} organizationId - Organization ID
 * @param {string} paymentType - Payment type from PaymentType enum
 * @returns {Object} Validation result with success flag and message
 */
async function validateBillingFeatureAccess(userId, organizationId, paymentType) {
  try {
    logInfo(`Validating billing feature access for user: ${userId}, organization: ${organizationId}, paymentType: ${paymentType}`)
    
    // Validate payment type
    if (!Object.values(PaymentType).includes(paymentType)) {
      return {
        success: false,
        message: `Invalid payment type: ${paymentType}. Allowed values: ${Object.values(PaymentType).join(', ')}`,
        errorCode: 'INVALID_PAYMENT_TYPE'
      }
    }
    
    // Check if user has access to the billing feature
    const hasAccess = await billingFeatureService.hasAccessToBillingFeature(userId, organizationId, paymentType)
    
    if (!hasAccess) {
      const featureNames = {
        [PaymentType.PATIENT_REGISTRATION]: 'Patient Registration',
        [PaymentType.CONSULTATION]: 'Appointment Booking',
        [PaymentType.LAB_TEST]: 'Lab Master',
        [PaymentType.PRESCRIPTION]: 'Prescription'
      }
      
      return {
        success: false,
        message: `Access denied. User does not have permission for ${featureNames[paymentType]} billing feature.`,
        errorCode: 'BILLING_FEATURE_ACCESS_DENIED'
      }
    }
    
    return {
      success: true,
      message: 'Billing feature access validated successfully'
    }
  } catch (error) {
    logError(`Error validating billing feature access for user ${userId}, paymentType ${paymentType}`, error)
    return {
      success: false,
      message: 'Internal error occurred while validating billing feature access',
      errorCode: 'VALIDATION_ERROR'
    }
  }
}

/**
 * Validate billing feature access for multiple payment types
 * @param {string} userId - User ID
 * @param {string} organizationId - Organization ID
 * @param {Array<string>} paymentTypes - Array of payment types
 * @returns {Object} Validation results for each payment type
 */
async function validateMultipleBillingFeatureAccess(userId, organizationId, paymentTypes) {
  try {
    logInfo(`Validating multiple billing feature access for user: ${userId}, organization: ${organizationId}`)
    
    const results = {}
    
    for (const paymentType of paymentTypes) {
      results[paymentType] = await validateBillingFeatureAccess(userId, organizationId, paymentType)
    }
    
    return {
      success: true,
      results
    }
  } catch (error) {
    logError(`Error validating multiple billing feature access for user ${userId}`, error)
    return {
      success: false,
      message: 'Internal error occurred while validating multiple billing feature access',
      errorCode: 'VALIDATION_ERROR'
    }
  }
}

/**
 * Get billing feature access summary for a user
 * @param {string} userId - User ID
 * @param {string} organizationId - Organization ID
 * @returns {Object} Summary of billing feature access
 */
async function getBillingFeatureAccessSummary(userId, organizationId) {
  try {
    logInfo(`Getting billing feature access summary for user: ${userId}, organization: ${organizationId}`)
    
    const billingAccess = await billingFeatureService.getBillingFeatureAccess(userId, organizationId)
    
    if (!billingAccess) {
      return {
        success: false,
        message: 'Unable to retrieve billing feature access',
        errorCode: 'ACCESS_RETRIEVAL_ERROR'
      }
    }
    
    const summary = {
      userId,
      organizationId,
      billingFeatures: billingAccess.billingFeatures,
      isDefault: billingAccess.isDefault || false,
      lastUpdated: billingAccess.updated_on,
      accessiblePaymentTypes: []
    }
    
    // Map billing features to payment types
    const featureToPaymentTypeMap = {
      patientRegistration: PaymentType.PATIENT_REGISTRATION,
      appointmentBooking: PaymentType.CONSULTATION,
      labMaster: PaymentType.LAB_TEST,
      prescription: PaymentType.PRESCRIPTION
    }
    
    // Get accessible payment types
    for (const [feature, paymentType] of Object.entries(featureToPaymentTypeMap)) {
      if (billingAccess.billingFeatures[feature]) {
        summary.accessiblePaymentTypes.push(paymentType)
      }
    }
    
    return {
      success: true,
      data: summary
    }
  } catch (error) {
    logError(`Error getting billing feature access summary for user ${userId}`, error)
    return {
      success: false,
      message: 'Internal error occurred while getting billing feature access summary',
      errorCode: 'SUMMARY_ERROR'
    }
  }
}

/**
 * Middleware function to validate billing feature access before payment operations
 * @param {string} userId - User ID
 * @param {string} organizationId - Organization ID
 * @param {string} paymentType - Payment type
 * @returns {Function} Middleware function
 */
function billingFeatureAccessMiddleware(userId, organizationId, paymentType) {
  return async function(req, res, next) {
    try {
      const validation = await validateBillingFeatureAccess(userId, organizationId, paymentType)
      
      if (!validation.success) {
        return res.status(403).json({
          error: validation.message,
          errorCode: validation.errorCode
        })
      }
      
      // Add validation result to request for further use
      req.billingValidation = validation
      next()
    } catch (error) {
      logError('Error in billing feature access middleware', error)
      return res.status(500).json({
        error: 'Internal server error during billing validation',
        errorCode: 'MIDDLEWARE_ERROR'
      })
    }
  }
}

module.exports = {
  validateBillingFeatureAccess,
  validateMultipleBillingFeatureAccess,
  getBillingFeatureAccessSummary,
  billingFeatureAccessMiddleware
}
