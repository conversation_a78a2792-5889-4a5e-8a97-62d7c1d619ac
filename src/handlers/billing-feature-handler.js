const billingFeatureService = require('../services/billing-feature-service')
const { logError, logInfo } = require('../common/logging')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')

class BillingFeatureHandler {
  /**
   * Get billing feature access settings for a user
   * @param {Object} req - Request object
   * @returns {Object} Response with billing feature access settings
   */
  async getBillingFeatureAccess(req) {
    try {
      const userId = req.query.get('userId')
      const organizationId = req.query.get('organizationId')

      if (!userId) {
        return jsonResponse('Missing userId parameter', HttpStatusCode.BadRequest)
      }

      if (!organizationId) {
        return jsonResponse('Missing organizationId parameter', HttpStatusCode.BadRequest)
      }

      logInfo(`Getting billing feature access for user: ${userId}, organization: ${organizationId}`)
      
      const billingAccess = await billingFeatureService.getBillingFeatureAccess(userId, organizationId)
      
      if (!billingAccess) {
        return jsonResponse('Unable to retrieve billing feature access', HttpStatusCode.InternalServerError)
      }

      return jsonResponse(billingAccess, HttpStatusCode.Ok)
    } catch (error) {
      logError('Error in getBillingFeatureAccess handler', error)
      return jsonResponse('Failed to get billing feature access', HttpStatusCode.InternalServerError)
    }
  }

  /**
   * Update billing feature access settings for a user
   * @param {Object} req - Request object
   * @returns {Object} Response with updated billing feature access
   */
  async updateBillingFeatureAccess(req) {
    try {
      const requestBody = await req.json()
      const { userId, organizationId, billingFeatures } = requestBody

      if (!userId) {
        return jsonResponse('Missing userId in request body', HttpStatusCode.BadRequest)
      }

      if (!organizationId) {
        return jsonResponse('Missing organizationId in request body', HttpStatusCode.BadRequest)
      }

      if (!billingFeatures || typeof billingFeatures !== 'object') {
        return jsonResponse('Missing or invalid billingFeatures in request body', HttpStatusCode.BadRequest)
      }

      // Validate billing features structure
      const requiredFeatures = ['patientRegistration', 'appointmentBooking', 'labMaster', 'prescription']
      const providedFeatures = Object.keys(billingFeatures)
      
      for (const feature of requiredFeatures) {
        if (!providedFeatures.includes(feature)) {
          return jsonResponse(`Missing billing feature: ${feature}`, HttpStatusCode.BadRequest)
        }
        
        if (typeof billingFeatures[feature] !== 'boolean') {
          return jsonResponse(`Invalid value for billing feature ${feature}. Expected boolean.`, HttpStatusCode.BadRequest)
        }
      }

      logInfo(`Updating billing feature access for user: ${userId}, organization: ${organizationId}`)
      
      const billingFeatureData = {
        userId,
        organizationId,
        billingFeatures,
        isDefault: false
      }

      const result = await billingFeatureService.upsertBillingFeatureAccess(billingFeatureData)
      
      if (!result) {
        return jsonResponse('Unable to update billing feature access', HttpStatusCode.InternalServerError)
      }

      return jsonResponse({
        message: 'Billing feature access updated successfully',
        data: result
      }, HttpStatusCode.Ok)
    } catch (error) {
      logError('Error in updateBillingFeatureAccess handler', error)
      return jsonResponse('Failed to update billing feature access', HttpStatusCode.InternalServerError)
    }
  }

  /**
   * Check if user has access to a specific billing feature
   * @param {Object} req - Request object
   * @returns {Object} Response with access check result
   */
  async checkBillingFeatureAccess(req) {
    try {
      const userId = req.query.get('userId')
      const organizationId = req.query.get('organizationId')
      const paymentType = req.query.get('paymentType')

      if (!userId) {
        return jsonResponse('Missing userId parameter', HttpStatusCode.BadRequest)
      }

      if (!organizationId) {
        return jsonResponse('Missing organizationId parameter', HttpStatusCode.BadRequest)
      }

      if (!paymentType) {
        return jsonResponse('Missing paymentType parameter', HttpStatusCode.BadRequest)
      }

      logInfo(`Checking billing feature access for user: ${userId}, organization: ${organizationId}, paymentType: ${paymentType}`)
      
      const hasAccess = await billingFeatureService.hasAccessToBillingFeature(userId, organizationId, paymentType)
      
      return jsonResponse({
        userId,
        organizationId,
        paymentType,
        hasAccess
      }, HttpStatusCode.Ok)
    } catch (error) {
      logError('Error in checkBillingFeatureAccess handler', error)
      return jsonResponse('Failed to check billing feature access', HttpStatusCode.InternalServerError)
    }
  }

  /**
   * Get available billing features based on user's role permissions
   * @param {Object} req - Request object
   * @returns {Object} Response with available billing features
   */
  async getAvailableBillingFeatures(req) {
    try {
      const userId = req.query.get('userId')
      const organizationId = req.query.get('organizationId')

      if (!userId) {
        return jsonResponse('Missing userId parameter', HttpStatusCode.BadRequest)
      }

      if (!organizationId) {
        return jsonResponse('Missing organizationId parameter', HttpStatusCode.BadRequest)
      }

      logInfo(`Getting available billing features for user: ${userId}, organization: ${organizationId}`)
      
      const defaultAccess = await billingFeatureService.getDefaultBillingFeatureAccess(userId, organizationId)
      
      if (!defaultAccess) {
        return jsonResponse('Unable to retrieve available billing features', HttpStatusCode.InternalServerError)
      }

      return jsonResponse({
        userId,
        organizationId,
        availableFeatures: defaultAccess.billingFeatures,
        message: 'Available billing features based on role permissions'
      }, HttpStatusCode.Ok)
    } catch (error) {
      logError('Error in getAvailableBillingFeatures handler', error)
      return jsonResponse('Failed to get available billing features', HttpStatusCode.InternalServerError)
    }
  }
}

module.exports = new BillingFeatureHandler()
