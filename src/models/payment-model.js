const CosmosDbMetadata = require('./CosmosDb-Metadata-model')
const { PaymentStatus, PaymentType } = require('../common/constant')

class PaymentModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)

    this.id = data.id || ''
    this.razorpayOrderId = data.razorpayOrderId || ''
    this.razorpayPaymentId = data.razorpayPaymentId || ''
    this.razorpaySignature = data.razorpaySignature || ''

    // Amount in smallest currency unit (paise for INR)
    this.amount = data.amount || 0
    this.currency = data.currency || 'INR'

    // Payment status
    if (data.status && !Object.values(PaymentStatus).includes(data.status)) {
      throw new Error(
        `Invalid payment status. Allowed values are: ${Object.values(
          PaymentStatus,
        ).join(', ')}`,
      )
    }
    this.status = data.status || PaymentStatus.CREATED

    // Payment type
    if (
      data.paymentType &&
      !Object.values(PaymentType).includes(data.paymentType)
    ) {
      throw new Error(
        `Invalid payment type. Allowed values are: ${Object.values(
          PaymentType,
        ).join(', ')}`,
      )
    }
    this.paymentType = data.paymentType || ''

    // Core identifiers
    this.patientId = data.patientId || ''
    this.organizationId = data.organizationId || ''
    this.userId = data.userId || ''
    this.description = data.description || ''

    // Timestamps
    this.createdAt = data.createdAt || new Date().toISOString()
    this.verifiedAt = data.verifiedAt || null

    // Failure handling
    this.failureReason = data.failureReason || null

    // Metadata for different payment types
    this.metadata = data.metadata || {}

    // Notes field for additional information
    this.notes = data.notes || {}

    // Validate required fields
    this.validate()
  }

  validate() {
    const errors = []

    if (!this.amount || this.amount <= 0) {
      errors.push('Amount must be greater than 0')
    }

    if (!this.paymentType) {
      errors.push('Payment type is required')
    }

    if (!this.patientId) {
      errors.push('Patient ID is required')
    }

    if (!this.organizationId) {
      errors.push('Organization ID is required')
    }

    if (!this.userId) {
      errors.push('User ID is required')
    }

    if (!this.description) {
      errors.push('Description is required')
    }

    if (errors.length > 0) {
      throw new Error(`Payment validation failed: ${errors.join(', ')}`)
    }
  }

  // Helper method to mark payment as completed
  markAsCompleted(razorpayPaymentId, razorpaySignature) {
    this.status = PaymentStatus.COMPLETED
    this.razorpayPaymentId = razorpayPaymentId
    this.razorpaySignature = razorpaySignature
    this.verifiedAt = new Date().toISOString()
  }

  // Helper method to mark payment as failed
  markAsFailed(failureReason) {
    this.status = PaymentStatus.FAILED
    this.failureReason = failureReason
  }

  // Helper method to get amount in rupees (from paise)
  getAmountInRupees() {
    return this.amount / 100
  }

  // Helper method to set amount in rupees (converts to paise)
  setAmountInRupees(amountInRupees) {
    this.amount = Math.round(amountInRupees * 100)
  }

  // Helper method to check if payment is successful
  isSuccessful() {
    return this.status === PaymentStatus.COMPLETED
  }

  // Helper method to check if payment is pending
  isPending() {
    return this.status === PaymentStatus.CREATED
  }

  // Helper method to check if payment has failed
  hasFailed() {
    return this.status === PaymentStatus.FAILED
  }
}

module.exports = PaymentModel
