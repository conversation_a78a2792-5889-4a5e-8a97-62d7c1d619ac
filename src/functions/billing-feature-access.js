const { app } = require('@azure/functions')
const billingFeatureHandler = require('../handlers/billing-feature-handler')

app.http('get-billing-feature-access', {
  methods: ['GET'],
  route: 'billing-feature-access',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Getting billing feature access')
    return await billingFeatureHandler.getBillingFeatureAccess(req)
  }
})

app.http('update-billing-feature-access', {
  methods: ['POST', 'PATCH'],
  route: 'billing-feature-access',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Updating billing feature access')
    return await billingFeatureHandler.updateBillingFeatureAccess(req)
  }
})

app.http('check-billing-feature-access', {
  methods: ['GET'],
  route: 'billing-feature-access/check',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Checking billing feature access')
    return await billingFeatureHandler.checkBillingFeatureAccess(req)
  }
})

app.http('get-available-billing-features', {
  methods: ['GET'],
  route: 'billing-feature-access/available',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Getting available billing features')
    return await billingFeatureHandler.getAvailableBillingFeatures(req)
  }
})
