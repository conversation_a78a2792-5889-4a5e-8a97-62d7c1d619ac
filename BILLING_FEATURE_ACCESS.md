# Billing Feature Access System

## Overview

The Billing Feature Access System provides granular control over which billing features users can access within the EMR system. This system allows administrators to assign billing permissions to roles, and doctors to customize their billing feature access through the EMR customization interface.

## Architecture

### Components

1. **Billing Feature Permissions** - New permission keys for billing features
2. **Billing Feature Service** - Core business logic for managing billing access
3. **Billing Feature Handler** - API request/response handling
4. **Billing Validation Utils** - Validation logic for payment operations
5. **EMR Customization Integration** - UI customization for billing features

### Database Schema

#### BillingFeatureAccess Container
```json
{
  "id": "uuid",
  "userId": "user-uuid",
  "organizationId": "org-uuid",
  "billingFeatures": {
    "patientRegistration": true,
    "appointmentBooking": true,
    "labMaster": false,
    "prescription": true
  },
  "isDefault": false,
  "created_on": "2024-01-01T10:00:00.000Z",
  "updated_on": "2024-01-01T10:00:00.000Z"
}
```

## Billing Feature Permissions

### New Permission Keys

1. `billing.patient-registration.access` - Access to Patient Registration billing
2. `billing.appointment-booking.access` - Access to Appointment Booking billing  
3. `billing.lab-master.access` - Access to Lab Master billing
4. `billing.prescription.access` - Access to Prescription billing

### Permission Mapping

| Billing Feature | Payment Type | Permission Key |
|----------------|--------------|----------------|
| Patient Registration | `patient_registration` | `billing.patient-registration.access` |
| Appointment Booking | `consultation` | `billing.appointment-booking.access` |
| Lab Master | `lab_test` | `billing.lab-master.access` |
| Prescription | `prescription` | `billing.prescription.access` |

## API Endpoints

### Get Billing Feature Access
```http
GET /billing-feature-access?userId={userId}&organizationId={organizationId}
```

**Response:**
```json
{
  "userId": "user-uuid",
  "organizationId": "org-uuid",
  "billingFeatures": {
    "patientRegistration": true,
    "appointmentBooking": true,
    "labMaster": false,
    "prescription": true
  },
  "isDefault": false,
  "created_on": "2024-01-01T10:00:00.000Z",
  "updated_on": "2024-01-01T10:00:00.000Z"
}
```

### Update Billing Feature Access
```http
POST /billing-feature-access
```

**Request Body:**
```json
{
  "userId": "user-uuid",
  "organizationId": "org-uuid",
  "billingFeatures": {
    "patientRegistration": true,
    "appointmentBooking": false,
    "labMaster": true,
    "prescription": true
  }
}
```

### Check Billing Feature Access
```http
GET /billing-feature-access/check?userId={userId}&organizationId={organizationId}&paymentType={paymentType}
```

**Response:**
```json
{
  "userId": "user-uuid",
  "organizationId": "org-uuid",
  "paymentType": "consultation",
  "hasAccess": true
}
```

### Get Available Billing Features
```http
GET /billing-feature-access/available?userId={userId}&organizationId={organizationId}
```

**Response:**
```json
{
  "userId": "user-uuid",
  "organizationId": "org-uuid",
  "availableFeatures": {
    "patientRegistration": true,
    "appointmentBooking": true,
    "labMaster": true,
    "prescription": true
  },
  "message": "Available billing features based on role permissions"
}
```

## Payment API Integration

### Updated Create Payment Order

The payment creation API now requires `userId` field and validates billing feature access:

```http
POST /payments/create-order
```

**Request Body:**
```json
{
  "amount": 1500,
  "currency": "INR",
  "patientId": "patient-id",
  "paymentType": "consultation",
  "description": "Doctor consultation fee",
  "organizationId": "org-id",
  "userId": "user-id"
}
```

**Validation Flow:**
1. Validate required fields including `userId`
2. Validate payment type
3. **NEW:** Validate billing feature access for the user
4. Create payment order if validation passes

**Error Responses:**
```json
{
  "error": "Access denied. User does not have permission for Appointment Booking billing feature.",
  "status": 403
}
```

## EMR Customization Integration

### Billing Feature Access Section

The EMR customization screen now includes a "Billing Feature Access" section that shows checkboxes for:

- ☑️ Patient Registration
- ☑️ Appointment Booking  
- ☐ Lab Master
- ☑️ Prescription

### Behavior

1. **Admin assigns permissions** - Admin assigns billing permissions to roles through the role management interface
2. **Available features shown** - EMR customization shows only the billing features the user's role has permission for
3. **Doctor customizes** - Doctor can enable/disable available billing features in their EMR customization
4. **Payment validation** - Payment APIs validate against the doctor's customized billing feature access

## Implementation Flow

### 1. Admin Role Assignment
```
Admin → Role Management → Assign Billing Permissions → Role gets billing.*.access permissions
```

### 2. EMR Customization
```
Doctor → EMR Customization → Billing Feature Access → Enable/Disable available features
```

### 3. Payment Creation
```
Payment Request → Validate userId → Check billing feature access → Allow/Deny payment creation
```

## Testing

Run the test script to verify functionality:

```bash
node test-billing-features.js
```

### Test Coverage

- ✅ Billing Feature Service CRUD operations
- ✅ Billing Validation Utils
- ✅ Payment Integration validation
- ✅ Role permission integration
- ✅ EMR customization integration

## Security Considerations

1. **Role-based Access** - Billing features are controlled by role permissions
2. **User-level Customization** - Users can only disable features they have permission for
3. **Payment Validation** - All payment operations validate billing feature access
4. **Audit Trail** - All billing feature changes are logged with timestamps

## Migration Notes

### Existing Data
- Existing users will get default billing feature access based on their role permissions
- No data migration required for existing payment records

### API Changes
- Payment creation API now requires `userId` field
- New validation step added to payment flow
- Backward compatibility maintained for existing payment types

## Future Enhancements

1. **Organization-level Billing Settings** - Override billing features at organization level
2. **Time-based Access** - Temporary billing feature access
3. **Usage Analytics** - Track billing feature usage patterns
4. **Bulk Management** - Bulk update billing features for multiple users
