/**
 * Test script for billing feature access functionality
 * This script tests the complete flow from role permissions to billing feature access
 */

const billingFeatureService = require('./src/services/billing-feature-service')
const { validateBillingFeatureAccess, getBillingFeatureAccessSummary } = require('./src/utils/billing-validation')
const { PaymentType } = require('./src/common/constant')

// Mock data for testing
const testData = {
  userId: 'test-user-123',
  organizationId: 'test-org-456',
  roleId: 'test-role-789'
}

async function testBillingFeatureService() {
  console.log('🧪 Testing Billing Feature Service...\n')
  
  try {
    // Test 1: Get default billing feature access
    console.log('1. Testing getDefaultBillingFeatureAccess...')
    const defaultAccess = await billingFeatureService.getDefaultBillingFeatureAccess(
      testData.userId, 
      testData.organizationId
    )
    console.log('✅ Default access:', JSON.stringify(defaultAccess, null, 2))
    
    // Test 2: Create custom billing feature access
    console.log('\n2. Testing upsertBillingFeatureAccess...')
    const customBillingData = {
      userId: testData.userId,
      organizationId: testData.organizationId,
      billingFeatures: {
        patientRegistration: true,
        appointmentBooking: true,
        labMaster: false,
        prescription: true
      }
    }
    
    const upsertResult = await billingFeatureService.upsertBillingFeatureAccess(customBillingData)
    console.log('✅ Upsert result:', JSON.stringify(upsertResult, null, 2))
    
    // Test 3: Get billing feature access (should return custom settings)
    console.log('\n3. Testing getBillingFeatureAccess...')
    const billingAccess = await billingFeatureService.getBillingFeatureAccess(
      testData.userId, 
      testData.organizationId
    )
    console.log('✅ Billing access:', JSON.stringify(billingAccess, null, 2))
    
    // Test 4: Check specific billing feature access
    console.log('\n4. Testing hasAccessToBillingFeature...')
    const paymentTypes = Object.values(PaymentType)
    
    for (const paymentType of paymentTypes) {
      const hasAccess = await billingFeatureService.hasAccessToBillingFeature(
        testData.userId,
        testData.organizationId,
        paymentType
      )
      console.log(`✅ ${paymentType}: ${hasAccess ? '✓ Allowed' : '✗ Denied'}`)
    }
    
  } catch (error) {
    console.error('❌ Error in billing feature service test:', error.message)
  }
}

async function testBillingValidation() {
  console.log('\n🧪 Testing Billing Validation Utils...\n')
  
  try {
    // Test 1: Validate billing feature access for each payment type
    console.log('1. Testing validateBillingFeatureAccess...')
    const paymentTypes = Object.values(PaymentType)
    
    for (const paymentType of paymentTypes) {
      const validation = await validateBillingFeatureAccess(
        testData.userId,
        testData.organizationId,
        paymentType
      )
      console.log(`✅ ${paymentType}:`, validation.success ? '✓ Valid' : `✗ ${validation.message}`)
    }
    
    // Test 2: Get billing feature access summary
    console.log('\n2. Testing getBillingFeatureAccessSummary...')
    const summary = await getBillingFeatureAccessSummary(
      testData.userId,
      testData.organizationId
    )
    console.log('✅ Summary:', JSON.stringify(summary, null, 2))
    
    // Test 3: Test invalid payment type
    console.log('\n3. Testing invalid payment type...')
    const invalidValidation = await validateBillingFeatureAccess(
      testData.userId,
      testData.organizationId,
      'invalid_payment_type'
    )
    console.log('✅ Invalid payment type validation:', invalidValidation.success ? '✗ Should fail' : `✓ ${invalidValidation.message}`)
    
  } catch (error) {
    console.error('❌ Error in billing validation test:', error.message)
  }
}

async function testPaymentIntegration() {
  console.log('\n🧪 Testing Payment Integration...\n')
  
  try {
    // Test payment data structure
    console.log('1. Testing payment data structure...')
    const paymentData = {
      amount: 1500,
      currency: 'INR',
      patientId: 'patient-123',
      paymentType: PaymentType.CONSULTATION,
      description: 'Doctor consultation fee',
      organizationId: testData.organizationId,
      userId: testData.userId
    }
    
    console.log('✅ Payment data structure:', JSON.stringify(paymentData, null, 2))
    
    // Test validation before payment creation
    console.log('\n2. Testing validation before payment creation...')
    const prePaymentValidation = await validateBillingFeatureAccess(
      paymentData.userId,
      paymentData.organizationId,
      paymentData.paymentType
    )
    
    if (prePaymentValidation.success) {
      console.log('✅ Payment validation passed - payment can be created')
    } else {
      console.log(`✗ Payment validation failed: ${prePaymentValidation.message}`)
    }
    
  } catch (error) {
    console.error('❌ Error in payment integration test:', error.message)
  }
}

async function runAllTests() {
  console.log('🚀 Starting Billing Feature Access Tests\n')
  console.log('=' .repeat(60))
  
  await testBillingFeatureService()
  await testBillingValidation()
  await testPaymentIntegration()
  
  console.log('\n' + '='.repeat(60))
  console.log('🎉 All tests completed!')
  console.log('\n📋 Test Summary:')
  console.log('- ✅ Billing Feature Service: Tested CRUD operations')
  console.log('- ✅ Billing Validation Utils: Tested validation logic')
  console.log('- ✅ Payment Integration: Tested payment flow integration')
  console.log('\n💡 Next Steps:')
  console.log('1. Test with real database connection')
  console.log('2. Test role permission integration')
  console.log('3. Test EMR customization UI integration')
  console.log('4. Test payment API endpoints')
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error)
}

module.exports = {
  testBillingFeatureService,
  testBillingValidation,
  testPaymentIntegration,
  runAllTests
}
